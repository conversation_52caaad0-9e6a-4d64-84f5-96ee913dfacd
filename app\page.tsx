import { BenefitsSection } from "@/components/layout/sections/benefits";
import { CommunitySection } from "@/components/layout/sections/community";
import { ContactSection } from "@/components/layout/sections/contact";
import { FAQSection } from "@/components/layout/sections/faq";
import { FeaturesSection } from "@/components/layout/sections/features";
import { FooterSection } from "@/components/layout/sections/footer";
import { HeroSection } from "@/components/layout/sections/hero";
import { PricingSection } from "@/components/layout/sections/pricing";
import { ServicesSection } from "@/components/layout/sections/services";
import { SponsorsSection } from "@/components/layout/sections/sponsors";
import { TeamSection } from "@/components/layout/sections/team";
import { TestimonialSection } from "@/components/layout/sections/testimonial";
import { landingPageMetadata } from "@/components/seo/seo-utils";
import {
  OrganizationStructuredData,
  WebsiteStructuredData,
  SoftwareAppStructuredData,
  FAQStructuredData,
} from "@/components/seo/structured-data";

export const metadata = landingPageMetadata;

export default function Home() {
  return (
    <>
      {/* Structured Data */}
      <OrganizationStructuredData />
      <WebsiteStructuredData />
      <SoftwareAppStructuredData />
      <FAQStructuredData />

      {/* Page Content */}
      <HeroSection />
      <SponsorsSection />
      <BenefitsSection />
      <FeaturesSection />
      <ServicesSection />
      <TestimonialSection />
      <TeamSection />
      <CommunitySection />
      <PricingSection />
      <ContactSection />
      <FAQSection />
      <FooterSection />
    </>
  );
}
