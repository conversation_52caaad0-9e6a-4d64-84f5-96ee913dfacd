"use client";

import { Icon } from "@/components/ui/icon";
import { Marque<PERSON> } from "@devnomic/marquee";
import "@devnomic/marquee/dist/index.css";
import { icons } from "lucide-react";
interface sponsorsProps {
  icon: string;
  name: string;
}

const sponsors: sponsorsProps[] = [
  {
    icon: "Crown",
    name: "<PERSON>cme<PERSON>",
  },
  {
    icon: "Vega<PERSON>",
    name: "<PERSON><PERSON><PERSON><PERSON>",
  },
  {
    icon: "Ghost",
    name: "<PERSON><PERSON><PERSON>ponsor",
  },
  {
    icon: "Puzzle",
    name: "<PERSON><PERSON><PERSON><PERSON><PERSON>",
  },
  {
    icon: "Squirrel",
    name: "<PERSON><PERSON><PERSON>",
  },
  {
    icon: "<PERSON><PERSON>",
    name: "<PERSON><PERSON><PERSON>",
  },
  {
    icon: "Drama",
    name: "Acmetech",
  },
];

export const SponsorsSection = () => {
  return (
    <section id="sponsors" className="max-w-[75%] mx-auto pb-24 sm:pb-32">
      <h2 className="text-lg md:text-xl text-center mb-6">
        Our Platinum Sponsors
      </h2>

      <div className="mx-auto">
        <Marquee
          className="gap-[3rem]"
          fade
          innerClassName="gap-[3rem]"
          pauseOnHover
        >
          {sponsors.map(({ icon, name }) => (
            <div
              key={name}
              className="flex items-center text-xl md:text-2xl font-medium"
            >
              <Icon
                name={icon as keyof typeof icons}
                size={32}
                color="white"
                className="mr-2"
              />
              {name}
            </div>
          ))}
        </Marquee>
      </div>
    </section>
  );
};
