# SEO Implementation Guide

This guide documents the comprehensive SEO implementation for the Shadcn Landing Page using Next.js App Router and next-seo.

## Overview

The SEO implementation follows Next.js 13+ App Router best practices, combining:
- Native Next.js metadata API for meta tags
- Custom structured data components for JSON-LD
- Optimized configuration for search engines and social media

## Implementation Structure

### 1. Core Configuration (`next-seo.config.ts`)
- Centralized SEO configuration
- Default metadata settings
- Open Graph and Twitter card configurations
- Robots and indexing directives

### 2. Metadata Utilities (`components/seo/seo-utils.ts`)
- `generateMetadata()` function for creating page-specific metadata
- Pre-configured metadata objects for common pages
- Type-safe metadata generation

### 3. Structured Data (`components/seo/structured-data.tsx`)
- JSON-LD components using Next.js Script component
- Organization, Website, Software Application, and FAQ schemas
- Safe server-side rendering compatible

### 4. Legacy Components (Optional)
- `components/seo/seo-metadata.tsx` - NextSeo wrapper components
- `components/seo/json-ld.tsx` - next-seo JSON-LD components
- `components/seo/default-seo-provider.tsx` - Global SEO provider

## Key Features

### Meta Tags
- Comprehensive title and description optimization
- Proper canonical URLs
- Language alternates
- Viewport and theme color settings
- Author and application metadata

### Open Graph
- Website type optimization
- High-quality images (1200x630)
- Proper locale settings
- Site name and URL configuration

### Twitter Cards
- Summary large image cards
- Proper handle and site attribution
- Optimized descriptions and images

### Structured Data
- Organization schema for business information
- Website schema with search functionality
- Software Application schema for the template
- FAQ schema for common questions
- Breadcrumb support for navigation

### Search Engine Optimization
- Proper robots directives
- Sitemap-friendly structure
- Mobile-first responsive design
- Fast loading times
- Accessibility compliance

## Usage Examples

### Page-Level Metadata
```typescript
import { generateMetadata } from '@/components/seo/seo-utils';

export const metadata = generateMetadata({
  title: 'Custom Page Title',
  description: 'Custom page description',
  canonical: 'https://example.com/custom-page',
  keywords: ['keyword1', 'keyword2'],
});
```

### Structured Data
```tsx
import { OrganizationStructuredData } from '@/components/seo/structured-data';

export default function Page() {
  return (
    <>
      <OrganizationStructuredData />
      {/* Page content */}
    </>
  );
}
```

### Custom Breadcrumbs
```tsx
import { BreadcrumbStructuredData } from '@/components/seo/structured-data';

const breadcrumbItems = [
  { name: 'Home', url: 'https://example.com' },
  { name: 'Products', url: 'https://example.com/products' },
  { name: 'Current Page', url: 'https://example.com/products/item' },
];

<BreadcrumbStructuredData items={breadcrumbItems} />
```

## Best Practices

### 1. Title Optimization
- Keep titles under 60 characters
- Include primary keywords
- Use descriptive, unique titles for each page

### 2. Meta Descriptions
- Keep descriptions between 150-160 characters
- Include call-to-action phrases
- Avoid duplicate descriptions

### 3. Images
- Use high-quality images (minimum 1200x630 for Open Graph)
- Include descriptive alt text
- Optimize file sizes for fast loading

### 4. Structured Data
- Validate JSON-LD using Google's Rich Results Test
- Keep data accurate and up-to-date
- Follow Schema.org guidelines

### 5. Performance
- Minimize JavaScript for SEO components
- Use Next.js Script component for JSON-LD
- Implement proper caching strategies

## Testing and Validation

### Tools for Testing
1. **Google Search Console** - Monitor search performance
2. **Google Rich Results Test** - Validate structured data
3. **Facebook Sharing Debugger** - Test Open Graph tags
4. **Twitter Card Validator** - Verify Twitter cards
5. **Lighthouse SEO Audit** - Overall SEO score

### Validation Checklist
- [ ] Meta titles and descriptions are unique and descriptive
- [ ] Open Graph images display correctly
- [ ] Twitter cards render properly
- [ ] Structured data validates without errors
- [ ] Canonical URLs are correct
- [ ] Robots.txt allows crawling
- [ ] Sitemap is accessible and valid

## Maintenance

### Regular Updates
- Review and update meta descriptions quarterly
- Check for broken canonical URLs
- Update structured data when business information changes
- Monitor search console for crawl errors

### Performance Monitoring
- Track Core Web Vitals
- Monitor page load speeds
- Check mobile usability
- Review search rankings

## Troubleshooting

### Common Issues
1. **Duplicate meta tags** - Ensure only one metadata export per page
2. **Invalid JSON-LD** - Validate syntax and schema compliance
3. **Missing images** - Verify image URLs are accessible
4. **Incorrect canonical URLs** - Check metadataBase configuration

### Debug Mode
Enable Next.js debug mode to inspect metadata:
```bash
NEXT_DEBUG=1 npm run dev
```

## Resources

- [Next.js Metadata API](https://nextjs.org/docs/app/api-reference/functions/generate-metadata)
- [Schema.org Documentation](https://schema.org/)
- [Google SEO Guidelines](https://developers.google.com/search/docs)
- [Open Graph Protocol](https://ogp.me/)
- [Twitter Card Documentation](https://developer.twitter.com/en/docs/twitter-for-websites/cards)
