"use client";

import {
  OrganizationJsonLd as NextSeoOrganizationJsonLd,
  <PERSON>readcrumbJsonLd as NextSeoBreadcrumbJsonLd,
  FAQPageJsonLd as NextSeoFAQPageJsonLd,
  SoftwareAppJsonLd as NextSeoSoftwareAppJsonLd,
  WebPageJsonLd as NextSeoWebPageJsonLd,
} from "next-seo";
import { SITE_CONFIG } from "@/next-seo.config";

// Website JSON-LD
export function WebsiteJsonLd() {
  return (
    <NextSeoWebPageJsonLd
      useAppDir={true}
      description={SITE_CONFIG.description}
      id={`${SITE_CONFIG.url}/#webpage`}
      lastReviewed={new Date().toISOString()}
      reviewedBy={{
        type: "Organization",
        name: SITE_CONFIG.author,
      }}
    />
  );
}

// Organization JSON-LD
export function OrganizationJsonLd() {
  return (
    <NextSeoOrganizationJsonLd
      useAppDir={true}
      type="Organization"
      id={`${SITE_CONFIG.url}/#organization`}
      name={SITE_CONFIG.organization.name}
      url={SITE_CONFIG.url}
      logo={SITE_CONFIG.organization.logo}
      description={SITE_CONFIG.description}
      sameAs={[...SITE_CONFIG.organization.sameAs]}
      contactPoint={[
        {
          "@type": "ContactPoint",
          contactType: "customer service",
          availableLanguage: SITE_CONFIG.organization.contactLanguages,
        },
      ]}
    />
  );
}

// Software Application JSON-LD
export function SoftwareAppJsonLd() {
  return (
    <NextSeoSoftwareAppJsonLd
      useAppDir={true}
      name={`${SITE_CONFIG.name} Template`}
      price="0"
      priceCurrency="USD"
      aggregateRating={{
        ratingValue: "4.9",
        reviewCount: "150",
      }}
      applicationCategory="DeveloperApplication"
      operatingSystem="Web Browser"
      description={SITE_CONFIG.description}
      downloadUrl={SITE_CONFIG.organization.sameAs[0]}
      featureList={[
        "Responsive Design",
        "Dark Mode Support",
        "TypeScript",
        "Tailwind CSS",
        "Next.js 13+ App Router",
        "Shadcn/ui Components",
        "SEO Optimized",
        "Accessibility Features",
      ]}
      screenshot={SITE_CONFIG.ogImage}
    />
  );
}

// FAQ JSON-LD for FAQ section
export function FAQJsonLd() {
  return (
    <NextSeoFAQPageJsonLd
      useAppDir={true}
      mainEntity={[
        {
          questionName: "What is Shadcn Landing Page?",
          acceptedAnswerText:
            "Shadcn Landing Page is a free, modern, and responsive landing page template built with Shadcn/ui, Next.js, and Tailwind CSS. It provides a professional foundation for developers, startups, and businesses.",
        },
        {
          questionName: "Is it free to use?",
          acceptedAnswerText:
            "Yes, this landing page template is completely free to use for both personal and commercial projects. You can download, modify, and distribute it according to the open-source license.",
        },
        {
          questionName: "What technologies are used?",
          acceptedAnswerText:
            "The template is built with modern web technologies including Next.js 13+, React, TypeScript, Tailwind CSS, and Shadcn/ui components. It also includes features like dark mode support and responsive design.",
        },
        {
          questionName: "How do I get started?",
          acceptedAnswerText:
            "You can get started by cloning the repository from GitHub, installing the dependencies with npm or yarn, and running the development server. Detailed setup instructions are provided in the README file.",
        },
        {
          questionName: "Can I customize the design?",
          acceptedAnswerText:
            "Absolutely! The template is designed to be highly customizable. You can modify colors, fonts, layouts, and components to match your brand and requirements using Tailwind CSS and the component system.",
        },
        {
          questionName: "Is it mobile-friendly?",
          acceptedAnswerText:
            "Yes, the template is fully responsive and optimized for all device sizes, from mobile phones to desktop computers. It follows modern responsive design principles and best practices.",
        },
      ]}
    />
  );
}

// Breadcrumb JSON-LD for navigation
export function BreadcrumbJsonLd({
  items,
}: {
  items: Array<{ name: string; url: string }>;
}) {
  const breadcrumbItems = items.map((item, index) => ({
    position: index + 1,
    name: item.name,
    item: item.url,
  }));

  return (
    <NextSeoBreadcrumbJsonLd
      useAppDir={true}
      itemListElements={breadcrumbItems}
    />
  );
}
