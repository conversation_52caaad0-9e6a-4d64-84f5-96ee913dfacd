"use client";

import { NextSeo, NextSeoProps } from "next-seo";
import { WebPageJsonLd, OrganizationJsonLd } from "next-seo";
import { SITE_CONFIG } from "@/next-seo.config";

interface SEOMetadataProps extends NextSeoProps {
  jsonLd?: {
    type?: "webpage" | "organization" | "website";
    data?: any;
  };
}

export function SEOMetadata({ jsonLd, ...seoProps }: SEOMetadataProps) {
  return (
    <>
      <NextSeo {...seoProps} />

      {/* JSON-LD Structured Data */}
      {jsonLd?.type === "webpage" && jsonLd.data && (
        <WebPageJsonLd useAppDir={true} {...jsonLd.data} />
      )}

      {jsonLd?.type === "organization" && jsonLd.data && (
        <OrganizationJsonLd useAppDir={true} {...jsonLd.data} />
      )}

      {jsonLd?.type === "website" && jsonLd.data && (
        <WebPageJsonLd useAppDir={true} {...jsonLd.data} />
      )}
    </>
  );
}

// Landing Page SEO Component
export function LandingPageSEO() {
  return (
    <SEOMetadata
      title={SITE_CONFIG.title}
      description={SITE_CONFIG.description}
      canonical={SITE_CONFIG.url}
      openGraph={{
        type: "website",
        url: SITE_CONFIG.url,
        title: SITE_CONFIG.title,
        description: SITE_CONFIG.description,
        images: [
          {
            url: SITE_CONFIG.ogImage,
            width: 1200,
            height: 630,
            alt: `${SITE_CONFIG.name}`,
            type: "image/jpeg",
          },
        ],
        siteName: SITE_CONFIG.name,
      }}
      twitter={{
        handle: SITE_CONFIG.twitter.handle,
        site: SITE_CONFIG.twitter.site,
        cardType: "summary_large_image",
      }}
      jsonLd={{
        type: "website",
        data: {
          url: SITE_CONFIG.url,
          name: SITE_CONFIG.name,
          description: SITE_CONFIG.description,
          inLanguage: "en-US",
          potentialAction: [
            {
              "@type": "SearchAction",
              target: {
                "@type": "EntryPoint",
                urlTemplate: `${SITE_CONFIG.url}/search?q={search_term_string}`,
              },
              "query-input": "required name=search_term_string",
            },
          ],
        },
      }}
    />
  );
}

// Organization JSON-LD Component
export function OrganizationSEO() {
  return (
    <SEOMetadata
      jsonLd={{
        type: "organization",
        data: {
          type: "Organization",
          id: `${SITE_CONFIG.url}/#organization`,
          name: SITE_CONFIG.organization.name,
          url: SITE_CONFIG.url,
          logo: SITE_CONFIG.organization.logo,
          description: SITE_CONFIG.description,
          sameAs: SITE_CONFIG.organization.sameAs,
          contactPoint: [
            {
              "@type": "ContactPoint",
              contactType: "customer service",
              availableLanguage: SITE_CONFIG.organization.contactLanguages,
            },
          ],
        },
      }}
    />
  );
}
