import { Metada<PERSON> } from 'next';
import { SITE_CONFIG } from '@/next-seo.config';

interface SEOConfig {
  title: string;
  description: string;
  canonical?: string;
  noindex?: boolean;
  keywords?: string[];
  images?: {
    url: string;
    width: number;
    height: number;
    alt: string;
  }[];
}

const defaultImages = [
  {
    url: SITE_CONFIG.ogImage,
    width: 1200,
    height: 630,
    alt: `${SITE_CONFIG.name} - Modern React Components Template`,
  },
];

export function generateMetadata(config: SEOConfig): Metadata {
  const {
    title,
    description,
    canonical,
    noindex = false,
    keywords = [],
    images = defaultImages,
  } = config;

  return {
    title,
    description,
    keywords: keywords.join(', '),
    robots: {
      index: !noindex,
      follow: !noindex,
      googleBot: {
        index: !noindex,
        follow: !noindex,
        'max-video-preview': -1,
        'max-image-preview': 'large',
        'max-snippet': -1,
      },
    },
    openGraph: {
      title,
      description,
      url: canonical || SITE_CONFIG.url,
      siteName: SITE_CONFIG.name,
      images: images.map(img => ({
        url: img.url,
        width: img.width,
        height: img.height,
        alt: img.alt,
      })),
      locale: 'en_US',
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      site: SITE_CONFIG.twitter.site,
      creator: SITE_CONFIG.twitter.handle,
      images: images.map(img => img.url),
    },
    alternates: {
      canonical: canonical || SITE_CONFIG.url,
      languages: {
        'en-US': SITE_CONFIG.url,
        'pt-BR': `${SITE_CONFIG.url}/pt-br`,
      },
    },
    other: {
      'theme-color': '#000000',
      'color-scheme': 'dark light',
    },
  };
}

// Predefined metadata for common pages
export const landingPageMetadata = generateMetadata({
  title: SITE_CONFIG.title,
  description: SITE_CONFIG.description,
  canonical: SITE_CONFIG.url,
  keywords: SITE_CONFIG.keywords.split(', '),
});

export const featuresPageMetadata = generateMetadata({
  title: `Features - ${SITE_CONFIG.name} Components`,
  description: `Explore the powerful features of our ${SITE_CONFIG.name} template including responsive design, dark mode, accessibility, and modern React components.`,
  canonical: `${SITE_CONFIG.url}/features`,
  keywords: [
    'features',
    'shadcn components',
    'responsive design',
    'dark mode',
    'accessibility',
    'react features',
  ],
});

export const pricingPageMetadata = generateMetadata({
  title: `Pricing - ${SITE_CONFIG.name} Template`,
  description: `Choose the perfect plan for your project. Our ${SITE_CONFIG.name} template offers flexible pricing options for individuals, teams, and enterprises.`,
  canonical: `${SITE_CONFIG.url}/pricing`,
  keywords: [
    'pricing',
    'plans',
    'shadcn template',
    'subscription',
    'free template',
    'premium features',
  ],
});

export const contactPageMetadata = generateMetadata({
  title: `Contact Us - ${SITE_CONFIG.name}`,
  description: `Get in touch with our team. We're here to help you with your ${SITE_CONFIG.name} template questions and support needs.`,
  canonical: `${SITE_CONFIG.url}/contact`,
  keywords: [
    'contact',
    'support',
    'help',
    'shadcn template',
    'customer service',
    'get in touch',
  ],
});

// Utility function to generate section-specific metadata
export function generateSectionMetadata(section: string, description: string) {
  return generateMetadata({
    title: `${section} - Shadcn Landing Page`,
    description,
    canonical: `https://shadcn-landing-page.vercel.app/#${section.toLowerCase()}`,
    keywords: [
      section.toLowerCase(),
      'shadcn',
      'landing page',
      'react components',
      'nextjs',
      'tailwind',
    ],
  });
}

// Section-specific metadata generators
export const heroSectionMetadata = generateSectionMetadata(
  'Hero',
  'Stunning hero section with modern design and call-to-action buttons for your landing page.'
);

export const featuresSectionMetadata = generateSectionMetadata(
  'Features',
  'Explore the powerful features of our Shadcn landing page template including responsive design and modern components.'
);

export const testimonialsSectionMetadata = generateSectionMetadata(
  'Testimonials',
  'Read what our users say about the Shadcn landing page template and their success stories.'
);

export const teamSectionMetadata = generateSectionMetadata(
  'Team',
  'Meet the talented team behind the Shadcn landing page template and learn about our expertise.'
);

export const faqSectionMetadata = generateSectionMetadata(
  'FAQ',
  'Frequently asked questions about the Shadcn landing page template, setup, and customization.'
);
