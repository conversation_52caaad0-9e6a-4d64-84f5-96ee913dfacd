import Script from "next/script";
import { SITE_CONFIG } from "@/next-seo.config";

// Organization structured data
export function OrganizationStructuredData() {
  const organizationData = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "@id": `${SITE_CONFIG.url}/#organization`,
    name: SITE_CONFIG.organization.name,
    url: SITE_CONFIG.url,
    logo: SITE_CONFIG.organization.logo,
    description: SITE_CONFIG.description,
    sameAs: SITE_CONFIG.organization.sameAs,
    contactPoint: [
      {
        "@type": "ContactPoint",
        contactType: "customer service",
        availableLanguage: SITE_CONFIG.organization.contactLanguages,
      },
    ],
  };

  return (
    <Script
      id="organization-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(organizationData),
      }}
    />
  );
}

// Website structured data
export function WebsiteStructuredData() {
  const websiteData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "@id": `${SITE_CONFIG.url}/#website`,
    url: SITE_CONFIG.url,
    name: SITE_CONFIG.name,
    description: SITE_CONFIG.description,
    inLanguage: "en-US",
    potentialAction: [
      {
        "@type": "SearchAction",
        target: {
          "@type": "EntryPoint",
          urlTemplate: `${SITE_CONFIG.url}/search?q={search_term_string}`,
        },
        "query-input": "required name=search_term_string",
      },
    ],
  };

  return (
    <Script
      id="website-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(websiteData),
      }}
    />
  );
}

// Software Application structured data
export function SoftwareAppStructuredData() {
  const softwareData = {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    name: `${SITE_CONFIG.name} Template`,
    description: SITE_CONFIG.description,
    applicationCategory: "DeveloperApplication",
    operatingSystem: "Web Browser",
    price: "0",
    priceCurrency: "USD",
    downloadUrl: SITE_CONFIG.organization.sameAs[0], // GitHub URL
    screenshot: SITE_CONFIG.ogImage,
    aggregateRating: {
      "@type": "AggregateRating",
      ratingValue: "4.9",
      reviewCount: "150",
    },
    featureList: [
      "Responsive Design",
      "Dark Mode Support",
      "TypeScript",
      "Tailwind CSS",
      "Next.js 13+ App Router",
      "Shadcn/ui Components",
      "SEO Optimized",
      "Accessibility Features",
    ],
  };

  return (
    <Script
      id="software-app-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(softwareData),
      }}
    />
  );
}

// FAQ structured data
export function FAQStructuredData() {
  const faqData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    mainEntity: [
      {
        "@type": "Question",
        name: "What is Shadcn Landing Page?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Shadcn Landing Page is a free, modern, and responsive landing page template built with Shadcn/ui, Next.js, and Tailwind CSS. It provides a professional foundation for developers, startups, and businesses.",
        },
      },
      {
        "@type": "Question",
        name: "Is it free to use?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Yes, this landing page template is completely free to use for both personal and commercial projects. You can download, modify, and distribute it according to the open-source license.",
        },
      },
      {
        "@type": "Question",
        name: "What technologies are used?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "The template is built with modern web technologies including Next.js 13+, React, TypeScript, Tailwind CSS, and Shadcn/ui components. It also includes features like dark mode support and responsive design.",
        },
      },
      {
        "@type": "Question",
        name: "How do I get started?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "You can get started by cloning the repository from GitHub, installing the dependencies with npm or yarn, and running the development server. Detailed setup instructions are provided in the README file.",
        },
      },
      {
        "@type": "Question",
        name: "Can I customize the design?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Absolutely! The template is designed to be highly customizable. You can modify colors, fonts, layouts, and components to match your brand and requirements using Tailwind CSS and the component system.",
        },
      },
      {
        "@type": "Question",
        name: "Is it mobile-friendly?",
        acceptedAnswer: {
          "@type": "Answer",
          text: "Yes, the template is fully responsive and optimized for all device sizes, from mobile phones to desktop computers. It follows modern responsive design principles and best practices.",
        },
      },
    ],
  };

  return (
    <Script
      id="faq-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(faqData),
      }}
    />
  );
}

// Breadcrumb structured data
export function BreadcrumbStructuredData({
  items,
}: {
  items: Array<{ name: string; url: string }>;
}) {
  const breadcrumbData = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    itemListElement: items.map((item, index) => ({
      "@type": "ListItem",
      position: index + 1,
      name: item.name,
      item: item.url,
    })),
  };

  return (
    <Script
      id="breadcrumb-structured-data"
      type="application/ld+json"
      dangerouslySetInnerHTML={{
        __html: JSON.stringify(breadcrumbData),
      }}
    />
  );
}
