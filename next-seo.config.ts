import { DefaultSeoProps } from 'next-seo';
import { Metadata } from 'next';

// ===== CENTRALIZED SEO CONFIGURATION =====
// All site information in one place for easy maintenance

// Basic site information
export const SITE_CONFIG = {
  name: "Shadcn Landing Page",
  title: "Shadcn Landing Page - Modern React Components",
  description: "Free, modern, and responsive landing page template built with Shadcn/ui, Next.js, and Tailwind CSS. Perfect for developers, startups, and businesses looking for a professional web presence.",
  url: "https://side-projects-next-template.vhhb1z.easypanel.host/",
  ogImage: "/og-images/OG.png",
  keywords: "shadcn, landing page, react, nextjs, tailwind, components, ui, template, free, modern, responsive",
  author: "Shadcn Landing Page Team",
  creator: "Shadcn Landing Page Team",

  // Social Media
  twitter: {
    handle: "@shadcn",
    site: "@shadcn",
  },

  // Business Info
  organization: {
    name: "Shadcn Landing Page",
    logo: "https://res.cloudinary.com/dbzv9xfjp/image/upload/v1723499276/og-images/shadcn-landing-page.png",
    contactLanguages: ["English", "Portuguese"],
    sameAs: [
      "https://github.com/nobruf/shadcn-landing-page",
      "https://twitter.com/shadcn",
    ],
  },
} as const;

// Configuration for next-seo (legacy)
const seoConfig: DefaultSeoProps = {
  title: SITE_CONFIG.title,
  titleTemplate: `%s | ${SITE_CONFIG.name}`,
  defaultTitle: SITE_CONFIG.title,
  description: SITE_CONFIG.description,
  canonical: SITE_CONFIG.url,

  // Robots and indexing
  robotsProps: {
    nosnippet: false,
    notranslate: false,
    noimageindex: false,
    noarchive: false,
    maxSnippet: -1,
    maxImagePreview: "large",
    maxVideoPreview: -1,
  },

  // Language and locale
  languageAlternates: [
    {
      hrefLang: "en-US",
      href: SITE_CONFIG.url,
    },
    {
      hrefLang: "pt-BR",
      href: `${SITE_CONFIG.url}/pt-br`,
    },
  ],

  // Open Graph
  openGraph: {
    type: "website",
    locale: "en_US",
    url: SITE_CONFIG.url,
    siteName: SITE_CONFIG.name,
    title: SITE_CONFIG.title,
    description: SITE_CONFIG.description,
    images: [
      {
        url: SITE_CONFIG.ogImage,
        width: 1200,
        height: 630,
        alt: `${SITE_CONFIG.name} - Modern React Components Template`,
        type: "image/jpeg",
      },
      {
        url: SITE_CONFIG.ogImage,
        width: 800,
        height: 600,
        alt: `${SITE_CONFIG.name} Preview`,
        type: "image/jpeg",
      },
    ],
  },

  // Twitter
  twitter: {
    handle: SITE_CONFIG.twitter.handle,
    site: SITE_CONFIG.twitter.site,
    cardType: "summary_large_image",
  },

  // Additional meta tags
  additionalMetaTags: [
    {
      name: "viewport",
      content: "width=device-width, initial-scale=1",
    },
    {
      name: "theme-color",
      content: "#000000",
    },
    {
      name: "author",
      content: SITE_CONFIG.author,
    },
    {
      name: "keywords",
      content: SITE_CONFIG.keywords,
    },
    {
      property: "dc:creator",
      content: SITE_CONFIG.creator,
    },
    {
      name: "application-name",
      content: SITE_CONFIG.name,
    },
    {
      name: "msapplication-TileColor",
      content: "#000000",
    },
  ],

  // Additional link tags
  additionalLinkTags: [
    {
      rel: "icon",
      href: "/favicon.ico",
    },
    {
      rel: "apple-touch-icon",
      href: "/favicon.ico",
      sizes: "76x76",
    },
    {
      rel: "manifest",
      href: "/manifest.json",
    },
  ],
};

// ===== METADATA FOR NEXT.JS APP ROUTER =====
// Function to generate centralized metadata
export function generateAppMetadata(overrides: Partial<Metadata> = {}): Metadata {
  return {
    title: SITE_CONFIG.title,
    description: SITE_CONFIG.description,
    keywords: SITE_CONFIG.keywords,
    authors: [{ name: SITE_CONFIG.author }],
    creator: SITE_CONFIG.creator,
    metadataBase: new URL(SITE_CONFIG.url),
    alternates: {
      canonical: "/",
      languages: {
        "en-US": "/",
        "pt-BR": "/pt-br",
      },
    },
    openGraph: {
      type: "website",
      locale: "en_US",
      url: SITE_CONFIG.url,
      siteName: SITE_CONFIG.name,
      title: SITE_CONFIG.title,
      description: SITE_CONFIG.description,
      images: [
        {
          url: SITE_CONFIG.ogImage,
          width: 1200,
          height: 630,
          alt: `${SITE_CONFIG.name} - Modern React Components Template`,
        },
      ],
    },
    twitter: {
      card: "summary_large_image",
      title: SITE_CONFIG.title,
      description: SITE_CONFIG.description,
      site: SITE_CONFIG.twitter.site,
      creator: SITE_CONFIG.twitter.handle,
      images: [SITE_CONFIG.ogImage],
    },
    robots: {
      index: true,
      follow: true,
      googleBot: {
        index: true,
        follow: true,
        "max-video-preview": -1,
        "max-image-preview": "large",
        "max-snippet": -1,
      },
    },
    ...overrides,
  };
}

export default seoConfig;